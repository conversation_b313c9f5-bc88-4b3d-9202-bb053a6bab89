from fastapi import FastAPI

app = FastAPI()

@app.get("/health")
def health():
    return {"status": "ok"}

from fastapi import UploadFile, File
# 临时使用 mock 版本，避免依赖问题
try:
    from .stt import transcribe
except ImportError:
    from .stt_mock import transcribe

@app.post("/stt")
async def stt_api(file: UploadFile = File(...)):
    """
    Upload an audio file and get Whisper transcription.
    """
    tmp_path = f"/tmp/{file.filename}"
    with open(tmp_path, "wb") as tmp:
        tmp.write(await file.read())
    return {"text": transcribe(tmp_path)}

from fastapi import UploadFile, File
# 临时使用 mock 版本，避免 pyannote.audio 依赖问题
try:
    from .diar import diarize
except ImportError:
    from .diar_mock import diarize
try:
    from .stt import transcribe_segmented
except ImportError:
    from .stt_mock import transcribe_segmented

@app.post("/diar-stt")
async def diar_stt_api(file: UploadFile = File(...)):
    """
    上传音频 → 说话人分离 → 分段转写
    """
    tmp_path = f"/tmp/{file.filename}"
    # 保存上传文件到临时路径
    with open(tmp_path, "wb") as tmp:
        tmp.write(await file.read())

    segments = diarize(tmp_path)
    results = transcribe_segmented(tmp_path, segments)
    return {"results": results}

from fastapi import Body
from .translate import translate

@app.post("/translate")
async def translate_api(
    text: str = Body(..., embed=True),
    src: str = Body("zh"),
    tgt: str = Body("en"),
):
    return {"translation": translate(text, src, tgt)}

from src.app.classify import classify

@app.post("/classify")
async def classify_api(text: str = Body(..., embed=True)):
    scores = classify(text)
    pred = max(scores, key=scores.get)
    return {"prediction": pred, "scores": scores}