#!/usr/bin/env python3
"""
简单测试脚本，验证项目核心功能
"""

def test_text_pipeline():
    """测试文本处理流水线"""
    print("=== 测试文本处理流水线 ===")
    try:
        from src.app.text_pipeline import run_text_pipeline
        
        test_text = """
        今天的会议讨论了以下几个重要议题：
        1. 项目进度汇报
        2. 下周的工作安排
        3. 预算审核
        
        决定事项：
        - 张三负责完成技术文档
        - 李四准备下周的演示
        - 王五联系供应商
        """
        
        result = run_text_pipeline(test_text)
        print("✅ 文本处理成功!")
        print(f"摘要: {result.get('summary', 'N/A')}")
        print(f"待办事项: {result.get('todos', 'N/A')}")
        print(f"会议类型: {result.get('meeting_type', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"❌ 文本处理失败: {e}")
        return False

def test_audio_pipeline():
    """测试音频处理流水线（使用mock）"""
    print("\n=== 测试音频处理流水线 (Mock) ===")
    try:
        from src.app.pipeline import run_pipeline
        
        # 使用一个假的音频文件路径，mock版本不会真正读取文件
        result = run_pipeline("fake_audio.wav")
        print("✅ 音频处理成功!")
        print(f"摘要: {result.get('summary', 'N/A')}")
        print(f"待办事项: {result.get('todos', 'N/A')}")
        print(f"会议类型: {result.get('meeting_type', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"❌ 音频处理失败: {e}")
        return False

def test_api_imports():
    """测试API相关导入"""
    print("\n=== 测试API模块导入 ===")
    try:
        from src.app.main import app
        print("✅ FastAPI应用导入成功!")
        return True
    except Exception as e:
        print(f"❌ API导入失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始测试会议摘要AI项目...")
    
    results = []
    results.append(test_api_imports())
    results.append(test_text_pipeline())
    results.append(test_audio_pipeline())
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过! 项目运行正常。")
        print("\n💡 启动建议:")
        print("1. FastAPI服务: python -m uvicorn src.app.main:app --host 0.0.0.0 --port 8000")
        print("2. 访问API文档: http://localhost:8000/docs")
        print("3. 健康检查: http://localhost:8000/health")
    else:
        print("⚠️  部分测试失败，请检查依赖安装。")

if __name__ == "__main__":
    main()
