# run_ui.py — Wizard‑of‑Oz demo front‑end
# --------------------------------------------------
# 简化版 Gradio UI，展示：
#   • 用户输入框（文字）
#   • AI 回复框（只读）
#   • 发送按钮
# 后台巫师只需在浏览器控制台执行：
#   window.postMessage({__wizard_reply: "你的回复"}, "*")
# 即可把文本注入 AI 回复框。
# --------------------------------------------------

# --------------------------------------------------
    #   JS 监听器：接收 window.postMessage 注入 AI 回复
    # --------------------------------------------------
js_listener = """
<script>
window.addEventListener('message', (event) => {
const data = event.data;
if (data && data.__wizard_reply) {
    const tb = document.querySelector('#ai_response textarea');
    if (!tb) return;
    tb.removeAttribute('disabled');                     // 1 解除只读
    tb.value = data.__wizard_reply;                     // 2 写入文字
    tb.dispatchEvent(new Event('input', {bubbles:true})); // 3 通知 Gradio
    }
});
</script>
"""

import gradio as gr
# 不再需要真实的pipeline，使用硬编码回复

# --------------------------------------------------
# 业务逻辑
# --------------------------------------------------

# 不再需要复杂的业务逻辑函数，直接使用硬编码回复


def on_submit_combined(audio_path, user_text: str):
    """
    处理音频文件 + 用户输入的组合提交，返回硬编码的 AI 回复。
    """
    # 检查是否有音频文件
    audio_status = "✅ 已上传音频文件" if audio_path else "❌ 未上传音频文件"

    # 检查是否有用户输入
    input_status = "✅ 已输入问题" if user_text.strip() else "❌ 未输入问题"

    # 硬编码的AI回复 - 优化的Markdown格式
    hardcoded_response = f"""# 🎯 会议分析报告

## 📊 处理状态
| 项目 | 状态 |
|------|------|
| 音频文件 | {audio_status} |
| 用户问题 | {input_status} |

---

## 📋 会议摘要

本次会议主要讨论了**项目进展情况**和**下一步工作安排**。与会人员就当前的技术方案进行了深入交流，并确定了关键里程碑的时间节点。

## 🎯 核心要点

- ✅ **项目整体进度**符合预期，技术架构基本确定
- ✅ **团队协作效率**有所提升，沟通机制进一步完善
- ✅ **下阶段重点**关注用户体验优化和性能提升

## 📝 待办事项

| 负责人 | 任务内容 | 截止日期 |
|--------|----------|----------|
| 张三 | 完成前端界面设计稿 | 本周五 |
| 李四 | 准备技术架构文档 | 下周二 |
| 王五 | 联系外部供应商确认合作细节 | 下周四 |
| 团队 | 准备下次评审会议材料 | 下周五 |

## 🏷️ 会议分类

**会议类型**: `项目进度汇报会议`

---

## 💡 AI 智能分析

> 这是一个典型的项目管理会议，重点关注进度跟踪和任务分配。建议后续加强跨部门协调，确保各项任务按时完成。

### 📈 会议质量评估

- **参与度评分**: `8.5/10` ⭐⭐⭐⭐⭐
- **会议效率**: `高效` 🚀
- **目标达成度**: `85%` 🎯

### 🔮 下次会议建议

1. **提前准备**: 各部门提前准备进度汇报材料
2. **时间控制**: 建议控制在60分钟内
3. **跟进机制**: 建立任务完成情况跟踪表"""

    return hardcoded_response

# --------------------------------------------------
# 前端布局
# --------------------------------------------------
with gr.Blocks(title="Meeting‑Notes AI (WoZ)") as demo:
    gr.Markdown("""
    ## Meeting‑Notes AI — Wizard‑of‑Oz 演示
    """)

    with gr.Row():
        audio_upload = gr.Audio(
            label="上传会议录音 (.wav)",
            type="filepath",
            elem_id="audio_input"
        )

        user_input = gr.Textbox(
            label="用户输入 (中文逐字稿)",
            placeholder="粘贴一段文字，例如会议逐字稿…",
            lines=4,
            autofocus=True,
            elem_id="user_input",
        )

        ai_output = gr.Markdown(
            label="AI 回复",
            value="",  # 初始为空
            elem_id="ai_response",  # 用于 JS 精准定位
        )

    send_btn = gr.Button("发送 / 分析")
    send_btn.click(
        on_submit_combined,
        inputs=[audio_upload, user_input],
        outputs=[ai_output]
    )
    gr.HTML(js_listener)

# --------------------------------------------------
# 调试入口
# --------------------------------------------------
if __name__ == "__main__":
    # Gradio 默认 http://127.0.0.1:7860
    demo.launch(server_name="127.0.0.1", server_port=7863)
