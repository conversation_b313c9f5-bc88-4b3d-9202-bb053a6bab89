# run_ui.py — Wizard‑of‑Oz demo front‑end
# --------------------------------------------------
# 简化版 Gradio UI，展示：
#   • 用户输入框（文字）
#   • AI 回复框（只读）
#   • 发送按钮
# 后台巫师只需在浏览器控制台执行：
#   window.postMessage({__wizard_reply: "你的回复"}, "*")
# 即可把文本注入 AI 回复框。
# --------------------------------------------------

# --------------------------------------------------
    #   JS 监听器：接收 window.postMessage 注入 AI 回复
    # --------------------------------------------------
js_listener = """
<script>
window.addEventListener('message', (event) => {
const data = event.data;
if (data && data.__wizard_reply) {
    const tb = document.querySelector('#ai_response textarea');
    if (!tb) return;
    tb.removeAttribute('disabled');                     // 1 解除只读
    tb.value = data.__wizard_reply;                     // 2 写入文字
    tb.dispatchEvent(new Event('input', {bubbles:true})); // 3 通知 Gradio
    }
});
</script>
"""

import gradio as gr
from src.app.pipeline import run_pipeline
from src.app.text_pipeline import run_text_pipeline

# --------------------------------------------------
# 业务逻辑
# --------------------------------------------------

def best_label(mdict):
    """取概率最高的会议类型标签（mdict 可能已经是字符串）。"""
    if isinstance(mdict, dict):
        return max(mdict, key=mdict.get)
    return mdict


def on_submit(user_text: str):
    """调用文字流水线，返回 AI 回复。"""
    if not user_text.strip():  # 空输入容错
        return "(⌒▽⌒) 请输入文本！", "", ""

    res = run_text_pipeline(user_text)

    summary = res.get("summary", "(mock) no summary")
    todos   = "\n".join(f"- {item.get('action','')}" for item in res.get("todos", []))
    mtype   = best_label(res.get("meeting_type", "unknown"))

    ai_text = f"【摘要】\n{summary}\n\n【待办】\n{todos}\n\n【会议类型】{mtype}"
    return ai_text, summary, todos

def on_audio_submit(audio_path):
    """
    处理上传的 .wav 文件，返回格式化好的 AI 回复。
    """
    if not audio_path:                 # 用户没选文件时给提示
        return "请先上传音频文件！"

    res = run_pipeline(audio_path)     # 调音频流水线
    summary = res.get("summary", "(mock) no summary")
    todos   = "\n".join(f"- {t['action']}" for t in res.get("todos", []))
    mtype   = best_label(res.get("meeting_type", "unknown"))

    ai_text = f"【摘要】\\n{summary}\\n\\n【待办】\\n{todos}\\n\\n【会议类型】{mtype}"
    return ai_text

# --------------------------------------------------
# 前端布局
# --------------------------------------------------
with gr.Blocks(title="Meeting‑Notes AI (WoZ)") as demo:
    gr.Markdown("""
    ## Meeting‑Notes AI — Wizard‑of‑Oz 演示
    """)

    with gr.Row():
        audio_upload = gr.Audio(
            label="上传会议录音 (.wav)",
            type="filepath",
            elem_id="audio_input"
        )

        user_input = gr.Textbox(
            label="用户输入 (中文逐字稿)",
            placeholder="粘贴一段文字，例如会议逐字稿…",
            lines=4,
            autofocus=True,
            elem_id="user_input",
        )

        ai_output = gr.Textbox(
            label="AI 回复 (只读)",
            lines=12,
            interactive=False,
            elem_id="ai_response",  # 用于 JS 精准定位
        )

    send_btn = gr.Button("发送 / 分析")
    send_btn.click(on_submit, inputs=user_input, outputs=[ai_output])
    send_btn.click(on_audio_submit, inputs=audio_upload, outputs=[ai_output])
    gr.HTML(js_listener)

# --------------------------------------------------
# 调试入口
# --------------------------------------------------
if __name__ == "__main__":
    # Gradio 默认 http://127.0.0.1:7860
    demo.launch(server_name="127.0.0.1", server_port=7861)
