# run_ui.py — Wizard‑of‑Oz demo front‑end
# --------------------------------------------------
# 简化版 Gradio UI，展示：
#   • 用户输入框（文字）
#   • AI 回复框（只读）
#   • 发送按钮
# 后台巫师只需在浏览器控制台执行：
#   window.postMessage({__wizard_reply: "你的回复"}, "*")
# 即可把文本注入 AI 回复框。
# --------------------------------------------------

# --------------------------------------------------
    #   JS 监听器：接收 window.postMessage 注入 AI 回复
    # --------------------------------------------------
js_listener = """
<script>
window.addEventListener('message', (event) => {
const data = event.data;
if (data && data.__wizard_reply) {
    const tb = document.querySelector('#ai_response textarea');
    if (!tb) return;
    tb.removeAttribute('disabled');                     // 1 解除只读
    tb.value = data.__wizard_reply;                     // 2 写入文字
    tb.dispatchEvent(new Event('input', {bubbles:true})); // 3 通知 Gradio
    }
});
</script>
"""

import gradio as gr
# 不再需要真实的pipeline，使用硬编码回复

# --------------------------------------------------
# 业务逻辑
# --------------------------------------------------

# 不再需要复杂的业务逻辑函数，直接使用硬编码回复


def on_submit_combined(audio_path, user_text: str, current_summary):
    """
    两步流程处理：
    1. 有音频无问题 → 生成初始摘要
    2. 有音频有问题 → 基于问题修改摘要
    """
    has_audio = audio_path is not None and audio_path != ""
    has_question = user_text.strip() != ""

    # 第一步：只有音频，没有问题 → 生成初始摘要
    if has_audio and not has_question:
        return generate_initial_summary()

    # 第二步：有音频且有问题 → 生成修改后的摘要
    elif has_audio and has_question:
        return generate_modified_summary(user_text)

    # 其他情况的提示
    elif not has_audio and has_question:
        return "⚠️ 请先上传音频文件，然后点击分析生成摘要。"
    else:
        return "📝 请上传音频文件并点击'发送/分析'按钮开始分析。"

def generate_initial_summary():
    """生成初始会议摘要"""
    return """# 🎯 会议分析报告

## 📊 处理状态
✅ 音频文件已处理
📝 等待用户问题

---

## 📋 会议摘要

本次会议主要讨论了**项目进展情况**和**下一步工作安排**。与会人员就当前的技术方案进行了深入交流，并确定了关键里程碑的时间节点。

## 🎯 核心要点

- ✅ **项目整体进度**符合预期，技术架构基本确定
- ✅ **团队协作效率**有所提升，沟通机制进一步完善
- ✅ **下阶段重点**关注用户体验优化和性能提升

## 📝 待办事项

| 负责人 | 任务内容 | 截止日期 |
|--------|----------|----------|
| 张三 | 完成前端界面设计稿 | 本周五 |
| 李四 | 准备技术架构文档 | 下周二 |
| 王五 | 联系外部供应商确认合作细节 | 下周四 |
| 团队 | 准备下次评审会议材料 | 下周五 |

## 🏷️ 会议分类

**会议类型**: `项目进度汇报会议`

---

## 💡 下一步操作

🔍 **请在用户输入框中提出您的问题**，例如：
- "重点关注技术风险有哪些？"
- "张三的任务能否提前完成？"
- "需要增加哪些资源投入？"

然后点击"发送/分析"按钮，AI将根据您的问题调整摘要内容。"""

def generate_modified_summary(user_question):
    """根据用户问题生成修改后的摘要"""

    # 硬编码的AI回复 - 根据用户问题调整的摘要
    hardcoded_response = f"""# 🎯 会议分析报告（已根据问题调整）

## � 处理状态
| 项目 | 状态 |
|------|------|
| 音频文件 | ✅ 已处理完成 |
| 用户问题 | ✅ "{user_question}" |

---

## 📋 会议摘要

本次会议主要讨论了**项目进展情况**和**下一步工作安排**。与会人员就当前的技术方案进行了深入交流，并确定了关键里程碑的时间节点。

## 🎯 核心要点

- ✅ **项目整体进度**符合预期，技术架构基本确定
- ✅ **团队协作效率**有所提升，沟通机制进一步完善
- ✅ **下阶段重点**关注用户体验优化和性能提升

## 📝 待办事项

| 负责人 | 任务内容 | 截止日期 |
|--------|----------|----------|
| 张三 | 完成前端界面设计稿 | 本周五 |
| 李四 | 准备技术架构文档 | 下周二 |
| 王五 | 联系外部供应商确认合作细节 | 下周四 |
| 团队 | 准备下次评审会议材料 | 下周五 |

## 🏷️ 会议分类

**会议类型**: `项目进度汇报会议`

---

## 💡 AI 智能分析

> 这是一个典型的项目管理会议，重点关注进度跟踪和任务分配。建议后续加强跨部门协调，确保各项任务按时完成。

### � 会议质量评估

- **参与度评分**: `8.5/10` ⭐⭐⭐⭐⭐
- **会议效率**: `高效` 🚀
- **目标达成度**: `85%` 🎯

### 🔮 下次会议建议

1. **提前准备**: 各部门提前准备进度汇报材料
2. **时间控制**: 建议控制在60分钟内
3. **跟进机制**: 建立任务完成情况跟踪表"""

    return hardcoded_response

def generate_todo_list(current_summary):
    """根据当前摘要生成待办事项列表"""

    todo_response = """# 📋 待办事项详细清单

## 🎯 基于会议摘要的行动计划

### 📊 任务概览
根据刚才的会议分析，系统为您整理了以下详细的待办事项清单：

---

## 🔥 高优先级任务

### 1. 前端界面设计
**负责人**: 张三
**截止时间**: 本周五
**任务详情**:
- 完成用户界面原型设计
- 制作交互流程图
- 准备设计规范文档
- 与产品经理确认设计方案

**📋 子任务清单**:
- [ ] 用户需求调研 (周一完成)
- [ ] 界面原型设计 (周三完成)
- [ ] 交互逻辑梳理 (周四完成)
- [ ] 设计文档整理 (周五完成)

---

### 2. 技术架构文档
**负责人**: 李四
**截止时间**: 下周二
**任务详情**:
- 系统架构设计文档
- 技术选型说明
- 数据库设计方案
- API接口规范

**📋 子任务清单**:
- [ ] 技术调研报告 (本周五完成)
- [ ] 架构设计图 (下周一完成)
- [ ] 接口文档编写 (下周二完成)

---

## ⚡ 中优先级任务

### 3. 供应商合作确认
**负责人**: 王五
**截止时间**: 下周四
**任务详情**:
- 联系外部供应商
- 确认合作细节和条款
- 签署合作协议
- 制定合作时间表

**📋 子任务清单**:
- [ ] 供应商资质审核 (下周一完成)
- [ ] 商务条款谈判 (下周三完成)
- [ ] 合同签署流程 (下周四完成)

---

### 4. 评审会议准备
**负责人**: 全体团队
**截止时间**: 下周五
**任务详情**:
- 准备项目进度汇报材料
- 整理技术难点和解决方案
- 制作演示PPT
- 准备Q&A环节

**📋 子任务清单**:
- [ ] 进度数据整理 (下周三完成)
- [ ] PPT制作 (下周四完成)
- [ ] 演示彩排 (下周五上午)

---

## 📈 任务统计

| 优先级 | 任务数量 | 负责人数 | 预计工时 |
|--------|----------|----------|----------|
| 高优先级 | 2个 | 2人 | 40小时 |
| 中优先级 | 2个 | 全员 | 30小时 |
| **总计** | **4个** | **全员** | **70小时** |

---

## 🔔 重要提醒

### ⚠️ 风险提示
- 前端设计任务时间紧张，建议提前沟通
- 技术架构需要与现有系统兼容性考虑
- 供应商选择需要多方案对比

### 💡 建议措施
- 建立每日站会机制跟踪进度
- 设置里程碑检查点
- 准备应急预案

### 📞 协调联系
如有任务冲突或资源需求，请及时联系项目经理进行协调。

---

*📅 清单生成时间: 2025-07-29*
*🔄 下次更新: 根据会议进展动态调整*"""

    return todo_response

# --------------------------------------------------
# 前端布局
# --------------------------------------------------
with gr.Blocks(title="Meeting‑Notes AI (WoZ)") as demo:
    gr.Markdown("""
    ## Meeting‑Notes AI — Wizard‑of‑Oz 演示
    """)

    with gr.Row():
        with gr.Column(scale=1):
            audio_upload = gr.Audio(
                label="上传会议录音 (.wav)",
                type="filepath",
                elem_id="audio_input"
            )

        with gr.Column(scale=1):
            user_input = gr.Textbox(
                label="用户输入 (中文逐字稿)",
                placeholder="粘贴一段文字，例如会议逐字稿…",
                lines=4,
                autofocus=True,
                elem_id="user_input",
            )

    # AI回复框单独放在一行，设置固定高度和滚动条
    ai_output = gr.Markdown(
        label="AI 回复",
        value="""# 📝 使用说明

## 🔄 两步操作流程

### 第一步：生成初始摘要
1. 上传音频文件（.wav格式）
2. **不要输入任何问题**
3. 点击"发送/分析"按钮
4. 系统将生成初始会议摘要

### 第二步：根据问题调整摘要
1. 在用户输入框中输入您的问题
2. 点击"发送/分析"按钮
3. 系统将根据您的问题生成调整后的摘要

---

💡 **提示**: 请按照上述步骤操作，先上传音频生成摘要，再提问获得定制化分析。""",
        elem_id="ai_response",
        height=400,  # 设置固定高度
    )

    with gr.Row():
        send_btn = gr.Button("发送 / 分析", variant="primary")
        todo_btn = gr.Button("📋 生成待办事项", variant="secondary")

    send_btn.click(
        on_submit_combined,
        inputs=[audio_upload, user_input, ai_output],
        outputs=[ai_output]
    )

    todo_btn.click(
        generate_todo_list,
        inputs=[ai_output],
        outputs=[ai_output]
    )
    gr.HTML(js_listener)

    # 添加CSS样式确保AI回复框有滚动条
    gr.HTML("""
    <style>
    #ai_response {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 5px;
    }
    #ai_response .markdown {
        max-height: 380px;
        overflow-y: auto;
    }
    </style>
    """)

# --------------------------------------------------
# 调试入口
# --------------------------------------------------
if __name__ == "__main__":
    # Gradio 默认 http://127.0.0.1:7860
    demo.launch(server_name="127.0.0.1", server_port=7863)
