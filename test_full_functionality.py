#!/usr/bin/env python3
"""
全面功能测试脚本
测试前后端的所有主要功能
"""

import requests
import json
import time
from pathlib import Path

# 配置
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://127.0.0.1:7862"

def test_backend_health():
    """测试后端健康检查"""
    print("=== 测试后端健康检查 ===")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端健康检查通过")
            print(f"响应: {response.json()}")
            return True
        else:
            print(f"❌ 后端健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_frontend_access():
    """测试前端访问"""
    print("\n=== 测试前端访问 ===")
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            return True
        else:
            print(f"❌ 前端访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        return False

def test_text_processing():
    """测试文本处理功能"""
    print("\n=== 测试文本处理功能 ===")
    try:
        from src.app.text_pipeline import run_text_pipeline
        
        test_text = """
        今天的项目会议讨论了以下重要事项：
        1. 产品开发进度回顾
        2. 下个月的里程碑规划
        3. 团队资源分配
        
        会议决定：
        - 张三负责完成用户界面设计
        - 李四准备技术架构文档
        - 王五联系外部供应商
        - 下周五前提交初步方案
        """
        
        result = run_text_pipeline(test_text)
        print("✅ 文本处理成功")
        print(f"摘要: {result.get('summary', 'N/A')}")
        print(f"待办事项数量: {len(result.get('todos', []))}")
        print(f"会议类型: {result.get('meeting_type', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"❌ 文本处理失败: {e}")
        return False

def test_audio_pipeline():
    """测试音频处理流水线（Mock版本）"""
    print("\n=== 测试音频处理流水线 ===")
    try:
        from src.app.pipeline import run_pipeline
        
        # 使用假的音频文件路径，Mock版本不会真正读取文件
        result = run_pipeline("fake_audio.wav")
        print("✅ 音频处理成功（Mock版本）")
        print(f"摘要: {result.get('summary', 'N/A')}")
        print(f"待办事项数量: {len(result.get('todos', []))}")
        print(f"会议类型: {result.get('meeting_type', 'N/A')}")
        return True
        
    except Exception as e:
        print(f"❌ 音频处理失败: {e}")
        return False

def test_api_endpoints():
    """测试主要API端点"""
    print("\n=== 测试API端点 ===")
    
    # 测试翻译API
    try:
        translate_data = {
            "text": "Hello, how are you?",
            "target_lang": "zh"
        }
        response = requests.post(
            f"{BACKEND_URL}/translate", 
            json=translate_data,
            timeout=10
        )
        if response.status_code == 200:
            print("✅ 翻译API正常")
            print(f"翻译结果: {response.json()}")
        else:
            print(f"⚠️ 翻译API响应异常: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 翻译API测试失败: {e}")
    
    # 测试其他端点的可访问性
    endpoints = ["/docs", "/openapi.json"]
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BACKEND_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} 可访问")
            else:
                print(f"⚠️ {endpoint} 响应异常: {response.status_code}")
        except Exception as e:
            print(f"⚠️ {endpoint} 测试失败: {e}")

def test_import_capabilities():
    """测试核心模块导入能力"""
    print("\n=== 测试核心模块导入 ===")
    
    modules_to_test = [
        ("FastAPI应用", "src.app.main", "app"),
        ("文本处理", "src.app.text_pipeline", "run_text_pipeline"),
        ("音频处理", "src.app.pipeline", "run_pipeline"),
        ("STT模块", "src.app.stt_mock", "transcribe"),
        ("说话人分离", "src.app.diar_mock", "diarize"),
    ]
    
    success_count = 0
    for name, module_path, attr_name in modules_to_test:
        try:
            module = __import__(module_path, fromlist=[attr_name])
            getattr(module, attr_name)
            print(f"✅ {name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {name} 导入失败: {e}")
    
    print(f"模块导入成功率: {success_count}/{len(modules_to_test)}")
    return success_count == len(modules_to_test)

def main():
    """运行所有测试"""
    print("🚀 开始全面功能测试...")
    print(f"后端地址: {BACKEND_URL}")
    print(f"前端地址: {FRONTEND_URL}")
    
    results = []
    
    # 运行所有测试
    results.append(("后端健康检查", test_backend_health()))
    results.append(("前端访问", test_frontend_access()))
    results.append(("核心模块导入", test_import_capabilities()))
    results.append(("文本处理", test_text_processing()))
    results.append(("音频处理", test_audio_pipeline()))
    
    # API测试（不计入总分）
    test_api_endpoints()
    
    # 统计结果
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 测试总结:")
    print(f"通过: {passed}/{total}")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 所有核心功能测试通过！")
        print("\n💡 使用建议:")
        print(f"1. 前端界面: {FRONTEND_URL}")
        print(f"2. API文档: {BACKEND_URL}/docs")
        print("3. 可以开始使用文本输入测试会议摘要功能")
        print("4. 音频功能目前使用Mock版本，返回示例数据")
    else:
        print(f"\n⚠️ {total - passed} 个测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
